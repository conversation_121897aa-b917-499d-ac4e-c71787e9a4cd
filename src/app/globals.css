@import url("https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&display=swap");

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9635 0.0032 17.2138);
  --foreground: oklch(0.2459 0.004 128.6429);
  --card: oklch(0.997 0 0);
  --card-foreground: oklch(0.2459 0.004 128.6429);
  --popover: oklch(0.997 0 0);
  --popover-foreground: oklch(0.2459 0.004 128.6429);
  --primary: oklch(0.2459 0.004 128.6429);
  --primary-foreground: oklch(0.9635 0.0032 17.2138);
  --secondary: oklch(0.5039 0.2196 273.7797);
  --secondary-foreground: oklch(0.9635 0.0032 17.2138);
  --muted: oklch(0.9491 0 0);
  --muted-foreground: oklch(0.3211 0 0);
  --accent: oklch(0.5707 0.2087 21.1426);
  --accent-foreground: oklch(0.2459 0.004 128.6429);
  --destructive: oklch(0.5707 0.2087 21.1426);
  --destructive-foreground: oklch(0.9635 0.0032 17.2138);
  --border: oklch(0.8975 0 0);
  --input: oklch(0.8452 0 0);
  --ring: oklch(0.5039 0.2196 273.7797);
  --chart-1: oklch(0.5854 0.2041 277.1173);
  --chart-2: oklch(0.5106 0.2301 276.9656);
  --chart-3: oklch(0.4568 0.2146 277.0229);
  --chart-4: oklch(0.3984 0.1773 277.3662);
  --chart-5: oklch(0.3588 0.1354 278.6973);
  --sidebar: oklch(0.8687 0.0043 56.366);
  --sidebar-foreground: oklch(0.2795 0.0368 260.031);
  --sidebar-primary: oklch(0.5854 0.2041 277.1173);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9376 0.026 321.9388);
  --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-border: oklch(0.8687 0.0043 56.366);
  --sidebar-ring: oklch(0.5854 0.2041 277.1173);
  --font-sans: Raleway, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Lora, ui-serif, serif;
  /* --font-mono: Space Mono, monospace; */
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 2px hsl(240 4% 60% / 0.05);
  --shadow-xs: 0px 4px 8px 2px hsl(240 4% 60% / 0.05);
  --shadow-sm: 0px 4px 8px 2px hsl(240 4% 60% / 0.1),
    0px 1px 2px 1px hsl(240 4% 60% / 0.1);
  --shadow: 0px 4px 8px 2px hsl(240 4% 60% / 0.1),
    0px 1px 2px 1px hsl(240 4% 60% / 0.1);
  --shadow-md: 0px 4px 8px 2px hsl(240 4% 60% / 0.1),
    0px 2px 4px 1px hsl(240 4% 60% / 0.1);
  --shadow-lg: 0px 4px 8px 2px hsl(240 4% 60% / 0.1),
    0px 4px 6px 1px hsl(240 4% 60% / 0.1);
  --shadow-xl: 0px 4px 8px 2px hsl(240 4% 60% / 0.1),
    0px 8px 10px 1px hsl(240 4% 60% / 0.1);
  --shadow-2xl: 0px 4px 8px 2px hsl(240 4% 60% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2459 0.004 128.6429);
  --foreground: oklch(0.9635 0.0032 17.2138);
  --card: #33333;
  --card-foreground: oklch(0.9635 0.0032 17.2138);
  --popover: #33333;
  --popover-foreground: oklch(0.9635 0.0032 17.2138);
  --primary: oklch(0.9635 0.0032 17.2138);
  --primary-foreground: oklch(0.2459 0.004 128.6429);
  --secondary: oklch(0.5039 0.2196 273.7797);
  --secondary-foreground: oklch(0.9635 0.0032 17.2138);
  --muted: oklch(0.3211 0 0);
  --muted-foreground: oklch(0.8975 0 0);
  --accent: oklch(0.5707 0.2087 21.1426);
  --accent-foreground: oklch(0.9635 0.0032 17.2138);
  --destructive: oklch(0.5707 0.2087 21.1426);
  --destructive-foreground: oklch(0.9635 0.0032 17.2138);
  --border: oklch(0.3211 0 0);
  --input: oklch(0.3211 0 0);
  --ring: oklch(0.5039 0.2196 273.7797);
  --chart-1: oklch(0.6801 0.1583 276.9349);
  --chart-2: oklch(0.5854 0.2041 277.1173);
  --chart-3: oklch(0.5106 0.2301 276.9656);
  --chart-4: oklch(0.4568 0.2146 277.0229);
  --chart-5: oklch(0.3984 0.1773 277.3662);
  --sidebar: oklch(0.3359 0.0077 59.4197);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.6801 0.1583 276.9349);
  --sidebar-primary-foreground: oklch(0.2244 0.0074 67.437);
  --sidebar-accent: oklch(0.3896 0.0074 59.4734);
  --sidebar-accent-foreground: oklch(0.8717 0.0093 258.3382);
  --sidebar-border: oklch(0.3359 0.0077 59.4197);
  --sidebar-ring: oklch(0.6801 0.1583 276.9349);
  --font-sans: Raleway, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Lora, ui-serif, serif;
  /* --font-mono: Space Mono, monospace; */
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 2px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px 2px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px 2px hsl(0 0% 0% / 0.1),
    0px 1px 2px 1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 4px 8px 2px hsl(0 0% 0% / 0.1),
    0px 1px 2px 1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 4px 8px 2px hsl(0 0% 0% / 0.1),
    0px 2px 4px 1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 4px 8px 2px hsl(0 0% 0% / 0.1),
    0px 4px 6px 1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 4px 8px 2px hsl(0 0% 0% / 0.1),
    0px 8px 10px 1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 4px 8px 2px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  /* font-family: Arial, Helvetica, sans-serif; */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
