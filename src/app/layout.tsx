import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { FooterBlock } from "@/blocks/Footer";
import { HeaderBlock } from "@/blocks/Header";

export const metadata: Metadata = {
  title:
    "MyBusinessFlow | Autonomous Growth Engine for Home Service Businesses",
  description: "Autonomous Growth Engine for Home Service Businesses.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        <HeaderBlock />
        {children}
        <FooterBlock />
      </body>
    </html>
  );
}
