export const FAQsBlock = () => {
  return (
    <section className="scroll-py-16 py-16 md:scroll-py-32 md:py-32">
      <div className="mx-auto max-w-5xl px-6">
        <div className="grid gap-y-12 px-2 lg:[grid-template-columns:1fr_auto]">
          <div className="text-center lg:text-left">
            <h2 className="mb-4 text-3xl font-semibold md:text-4xl">
              Frequently <br className="hidden lg:block" /> Asked{" "}
              <br className="hidden lg:block" />
              Questions
            </h2>
            <p>Accusantium quisquam. Illo, omnis?</p>
          </div>

          <div className="divide-y divide-dashed sm:mx-auto sm:max-w-lg lg:mx-0">
            <div className="pb-6">
              <h3 className="font-medium">What is the refund policy?</h3>
              <p className="text-muted-foreground mt-4">
                We offer a 30-day money back guarantee. If you are not satisfied
                with our product, you can request a refund within 30 days of
                your purchase.
              </p>

              <ol className="list-outside list-decimal space-y-2 pl-4">
                <li className="text-muted-foreground mt-4">
                  To request a refund, please contact our support team with your
                  order number and reason for the refund.
                </li>
                <li className="text-muted-foreground mt-4">
                  Refunds will be processed within 3-5 business days.
                </li>
                <li className="text-muted-foreground mt-4">
                  Please note that refunds are only available for new customers
                  and are limited to one per customer.
                </li>
              </ol>
            </div>
            <div className="py-6">
              <h3 className="font-medium">How do I cancel my subscription?</h3>
              <p className="text-muted-foreground mt-4">
                You can cancel your subscription at any time by logging into
                your account and clicking on the cancel button.
              </p>
            </div>
            <div className="py-6">
              <h3 className="font-medium">Can I upgrade my plan?</h3>
              <p className="text-muted-foreground my-4">
                Yes, you can upgrade your plan at any time by logging into your
                account and selecting the plan you want to upgrade to.
              </p>
              <ul className="list-outside list-disc space-y-2 pl-4">
                <li className="text-muted-foreground">
                  You will be charged the difference in price between your
                  current plan and the plan you are upgrading to.
                </li>
                <li className="text-muted-foreground">
                  Your new plan will take effect immediately and you will be
                  billed at the new rate on your next billing cycle.
                </li>
              </ul>
            </div>
            <div className="py-6">
              <h3 className="font-medium">Do you offer phone support?</h3>
              <p className="text-muted-foreground mt-4">
                We do not offer phone support at this time. However, you can
                contact us via email or live chat for any questions or concerns
                you may have.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
