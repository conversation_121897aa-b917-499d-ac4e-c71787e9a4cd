import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>et, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
// import { Logo } from "./logo";
import { HeaderNav } from "../Nav";

export const HeaderSheet = () => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon">
          <Menu />
        </Button>
      </SheetTrigger>
      <SheetContent side="left">
        {/* <Logo /> */}Logo
        <HeaderNav orientation="vertical" className="mt-12" />
      </SheetContent>
    </Sheet>
  );
};
