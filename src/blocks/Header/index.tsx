import { <PERSON><PERSON> } from "@/components/ui/button";
// import { Logo } from "./logo";
import { HeaderNav } from "./Nav";
import { HeaderSheet } from "./Sheet";

export const HeaderBlock = () => {
  return (
    <header className="">
      <nav className="h-16 bg-background border-b">
        <div className="h-full flex items-center justify-between max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* <Logo /> */} Logo
          {/* Desktop Menu */}
          <HeaderNav className="hidden md:block" />
          <div className="flex items-center gap-3">
            <Button variant="outline" className="hidden sm:inline-flex">
              Sign In
            </Button>
            <Button>Get Started</Button>
            {/* Mobile Menu */}
            <div className="md:hidden">
              <HeaderSheet />
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
};
